<template>
  <view class="home-container">

    <!-- 顶部操作栏 - 定位、搜索、加号 -->
    <view class="top-header" :class="{ 'sticky': isSticky }">
      <view class="header-content">
        <view class="location" @click="chooseLocation">
          <u-icon name="map" size="18" color="#666"></u-icon>
          <text class="location-text">{{ currentLocation || '获取位置中...' }}</text>
          <u-icon name="arrow-down" size="14" color="#999"></u-icon>
        </view>

        <view class="search-bar" @click="goSearch">
          <u-icon name="search" size="16" color="#999"></u-icon>
          <text class="search-placeholder">搜索任务、商品、商家</text>
          <view class="qr-btn" @click.stop="scanCode">
            <u-icon name="scan" size="16" color="#666"></u-icon>
          </view>
        </view>

        <view class="add-btn" @click="publishTask">
          <u-icon name="plus" size="18" color="#fff"></u-icon>
        </view>
      </view>
    </view>

    <!-- 吸顶占位元素 -->
    <view class="header-placeholder" v-if="isSticky"></view>

    <!-- 系统通知 -->
    <view class="notice-bar" v-if="notices.length > 0" @click="showNoticeList">
      <u-icon name="volume" size="32" color="#ff6b35"></u-icon>
      <swiper class="notice-swiper" vertical autoplay interval="3000" circular>
        <swiper-item v-for="notice in notices" :key="notice.noticeId">
          <text class="notice-text">{{ notice.noticeTitle }}</text>
        </swiper-item>
      </swiper>
      <u-icon name="arrow-right" size="24" color="#999"></u-icon>
    </view>

    <!-- 功能区域 -->
    <view class="function-area">
      <!-- 兴业助农 -->
      <view class="shopping-card" @click="goAgriculture">
        <image class="shopping-image" :src="getImageUrl(agricultureConfig.configImage)" mode="aspectFill"></image>
        <text class="shopping-title">{{ agricultureConfig.configName }}</text>
      </view>

      <!-- 购物专区 -->
      <view class="shopping-card" @click="goShopping">
        <image class="shopping-image" :src="getImageUrl(shoppingConfig.configImage)" mode="aspectFill"></image>
        <text class="shopping-title">{{ shoppingConfig.configName }}</text>
      </view>
    </view>

    <!-- 更多功能 -->
    <view class="more-functions">
      <view class="function-grid">
        <view class="function-item" v-for="func in functions" :key="func.functionId || func.id"
          @click="goFunction(func)">
          <image class="function-icon" :src="getImageUrl(func.functionIcon || func.icon)" mode="aspectFit"></image>
          <text class="function-name">{{ func.functionName || func.name }}</text>
        </view>
      </view>
    </view>

    <!-- 热门任务 -->
    <view class="hot-tasks">
      <view class="section-header">
        <text class="section-title">热门任务</text>
        <text class="more-btn" @click="goTaskList">更多</text>
      </view>
      <view class="task-list">
        <view class="task-item" v-for="task in hotTasks" :key="task.taskId" @click="goTaskDetail(task.taskId)">
          <view class="task-header">
            <view class="user-info">
              <image class="avatar" :src="getImageUrl(task.publisherAvatar) || '/static/default-avatar.png'"
                mode="aspectFill">
              </image>
              <view class="user-detail">
                <text class="username">{{ task.publisherName }}</text>
                <text class="publish-info">{{ formatTime(task.createTime) }} · {{ task.taskAddress }}</text>
              </view>
            </view>
            <view class="hot-score">
              <u-icon name="fire" size="24" color="#ff6b35"></u-icon>
              <text class="score">{{ task.hotScore || 0 }}</text>
            </view>
          </view>
          <view class="task-amount">¥{{ formatMoney(task.taskAmount) }}</view>
          <view class="task-title">{{ task.taskTitle }}</view>
          <view class="task-desc">{{ task.taskDesc }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getHomeData, getNotices, getAgricultureConfig, getShoppingConfig, getFunctions } from '@/api/home'
import { getHotTasks } from '@/api/task'
import { getCurrentLocation, formatTime, formatMoney, scanCode } from '@/utils/common'
import { getImageUrl } from '@/utils/request'

export default {
  data() {
    return {
      homeData: {},
      currentLocation: '',
      notices: [],
      agricultureConfig: {
      },
      shoppingConfig: {
      },
      functions: [],
      hotTasks: [],
      longitude: '',
      latitude: '',
      isSticky: false
    }
  },

  onLoad() {
    this.initPage()
  },

  onShow() {
    this.loadHotTasks()
  },

  onPageScroll(e) {
    // 当滚动超过60px时显示吸顶效果
    this.isSticky = e.scrollTop > 60
  },

  onReady() {
    // 页面渲染完成后可以进行一些初始化操作
  },

  methods: {
    // 获取图片完整URL
    getImageUrl,

    async initPage() {
      // 获取位置信息
      this.getLocation()

      // 加载页面数据
      this.loadHomeData()
      this.loadNotices()
      this.loadConfigs()
      this.loadFunctions()
    },

    async getLocation() {
      try {
        const location = await getCurrentLocation()
        this.longitude = location.longitude
        this.latitude = location.latitude
        this.currentLocation = location.address || '当前位置'
      } catch (error) {
        console.error('获取位置失败:', error)
        this.currentLocation = '定位失败'
      }
    },

    async loadHomeData() {
      try {
        const res = await getHomeData({
          longitude: this.longitude,
          latitude: this.latitude
        })
        this.homeData = res.data || {}
      } catch (error) {
        console.error('加载首页数据失败:', error)
      }
    },

    async loadNotices() {
      try {
        const res = await getNotices()
        this.notices = res.data || []
      } catch (error) {
        console.error('加载通知失败:', error)
      }
    },

    async loadConfigs() {
      try {
        const [agriculture, shopping] = await Promise.all([
          getAgricultureConfig(),
          getShoppingConfig()
        ])

        if (agriculture.data) {
          console.log(agriculture)
          this.agricultureConfig = agriculture.data
        }
        if (shopping.data) {
          this.shoppingConfig = shopping.data
        }
      } catch (error) {
        console.error('加载配置失败:', error)
      }
    },

    async loadHotTasks() {
      try {
        const res = await getHotTasks({
          longitude: this.longitude,
          latitude: this.latitude,
          limit: 10
        })
        this.hotTasks = res.data || []
      } catch (error) {
        console.error('加载热门任务失败:', error)
      }
    },

    async loadFunctions() {
      try {
        const res = await getFunctions()
        if (res.data && Array.isArray(res.data)) {
          this.functions = res.data
        }
      } catch (error) {
        console.error('加载功能配置失败:', error)
        // 如果加载失败，使用默认配置
        this.functions = [
          { functionId: 1, functionName: '我的钱包', functionIcon: '/static/icons/wallet.png', functionUrl: '/pages/wallet/index' },
          { functionId: 2, functionName: '我的任务', functionIcon: '/static/icons/task.png', functionUrl: '/pages/task/my' },
          { functionId: 3, functionName: '实名认证', functionIcon: '/static/icons/auth.png', functionUrl: '/pages/user/auth' },
          { functionId: 4, functionName: '客服中心', functionIcon: '/static/icons/service.png', functionUrl: '/pages/service/index' }
        ]
      }
    },

    publishTask() {
      uni.navigateTo({
        url: '/pages/task/publish'
      })
    },

    chooseLocation() {
      uni.chooseLocation({
        success: (res) => {
          this.currentLocation = res.name || res.address
          this.longitude = res.longitude
          this.latitude = res.latitude
          this.loadHotTasks()
        }
      })
    },

    goSearch() {
      uni.navigateTo({
        url: '/pages/search/index'
      })
    },

    async scanCode() {
      try {
        const result = await scanCode()
        // 处理扫码结果
        console.log('扫码结果:', result)
      } catch (error) {
        console.error('扫码失败:', error)
      }
    },

    showNoticeList() {
      uni.switchTab({
        url: '/pages/notice/list'
      })
    },

    goAgriculture() {
      if (this.agricultureConfig.linkUrl) {
        uni.navigateTo({
          url: this.agricultureConfig.linkUrl
        })
      }
    },

    goShopping() {
      if (this.shoppingConfig.linkUrl) {
        uni.navigateTo({
          url: this.shoppingConfig.linkUrl
        })
      }
    },

    goFunction(func) {
      const url = func.functionUrl || func.url
      if (url) {
        if (func.functionType === '1') {
          // 外部链接
          uni.navigateTo({
            url: '/pages/webview/index?url=' + encodeURIComponent(url)
          })
        } else {
          // 内部页面
          uni.navigateTo({
            url: url
          })
        }
      }
    },

    goTaskList() {
      uni.switchTab({
        url: '/pages/task/list'
      })
    },

    goTaskDetail(taskId) {
      uni.navigateTo({
        url: `/pages/task/detail?id=${taskId}`
      })
    },

    formatTime,
    formatMoney
  }
}
</script>

<style lang="scss" scoped>
.home-container {
  background: #f8f8f8;
  min-height: 100vh;
}

// 顶部操作栏样式
.top-header {
  position: relative;
  background: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
  z-index: 999;

  &.sticky {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10rpx);
    background: rgba(255, 255, 255, 0.95);
  }

  .header-content {
    display: flex;
    align-items: center;
    padding: 20rpx 30rpx;
    gap: 20rpx;

    .location {
      display: flex;
      align-items: center;
      flex-shrink: 0;
      padding: 12rpx 16rpx;
      background: #f8f9fa;
      border-radius: 24rpx;
      min-width: 140rpx;
      max-width: 180rpx;

      .location-text {
        margin: 0 8rpx;
        font-size: 24rpx;
        color: #333;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
      }

      &:active {
        background: #e9ecef;
      }
    }

    .search-bar {
      display: flex;
      align-items: center;
      background: #f8f9fa;
      border-radius: 28rpx;
      padding: 16rpx 20rpx;
      flex: 1;
      height: 56rpx;

      .search-placeholder {
        flex: 1;
        margin-left: 12rpx;
        font-size: 26rpx;
        color: #999;
      }

      .qr-btn {
        margin-left: 12rpx;
        padding: 4rpx;

        &:active {
          opacity: 0.7;
        }
      }

      &:active {
        background: #e9ecef;
      }
    }

    .add-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 56rpx;
      height: 56rpx;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 28rpx;
      flex-shrink: 0;
      box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);

      &:active {
        transform: scale(0.95);
        box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.4);
      }
    }
  }
}

// 吸顶占位元素
.header-placeholder {
  height: 96rpx; // 与header-content的高度保持一致
}

.notice-bar {
  display: flex;
  align-items: center;
  background: #fff7f0;
  padding: 20rpx 40rpx;
  margin-bottom: 20rpx;

  .notice-swiper {
    flex: 1;
    height: 60rpx;
    margin: 0 20rpx;

    .notice-text {
      font-size: 26rpx;
      color: #ff6b35;
      line-height: 60rpx;
    }
  }
}

.function-area {
  display: flex;
  padding: 0 40rpx;
  margin-bottom: 30rpx;
  gap: 20rpx;

  .shopping-card {
    flex: 1;
    background: #ffffff;
    border-radius: 20rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
    position: relative;

    .shopping-image {
      width: 100%;
      height: 280rpx;
    }

    .shopping-title {
      position: absolute;
      bottom: 20rpx;
      left: 20rpx;
      font-size: 32rpx;
      font-weight: bold;
      color: #ffffff;
      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
    }
  }
}

.more-functions {
  background: #ffffff;
  margin: 0 40rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 30rpx;
  }

  .function-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .function-item {
      width: 23%;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 30rpx;

      .function-icon {
        width: 80rpx;
        height: 80rpx;
        margin-bottom: 10rpx;
      }

      .function-name {
        font-size: 24rpx;
        color: #666;
        text-align: center;
        line-height: 1.2;
      }
    }
  }
}

.hot-tasks {
  background: #ffffff;
  margin: 0 40rpx;
  border-radius: 20rpx;
  padding: 30rpx;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .more-btn {
      font-size: 26rpx;
      color: #3cc51f;
    }
  }

  .task-list {
    .task-item {
      padding: 30rpx 0;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .task-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20rpx;

        .user-info {
          display: flex;
          align-items: center;

          .avatar {
            width: 80rpx;
            height: 80rpx;
            border-radius: 40rpx;
            margin-right: 20rpx;
          }

          .user-detail {
            .username {
              display: block;
              font-size: 28rpx;
              color: #333;
              margin-bottom: 5rpx;
            }

            .publish-info {
              font-size: 24rpx;
              color: #999;
            }
          }
        }

        .hot-score {
          display: flex;
          align-items: center;

          .score {
            margin-left: 5rpx;
            font-size: 24rpx;
            color: #ff6b35;
          }
        }
      }

      .task-amount {
        font-size: 36rpx;
        font-weight: bold;
        color: #ff6b35;
        margin-bottom: 10rpx;
      }

      .task-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 10rpx;
      }

      .task-desc {
        font-size: 28rpx;
        color: #666;
        line-height: 1.5;
      }
    }
  }
}
</style>
