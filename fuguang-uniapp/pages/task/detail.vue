<template>
  <view class="task-detail-container" v-if="task">
    <scroll-view class="detail-scroll" scroll-y refresher-enabled @refresherrefresh="onRefresh"
      :refresher-triggered="refreshing">
      <!-- 任务基本信息 -->
      <view class="task-info">
        <view class="task-header">
          <view class="user-info">
            <image class="avatar" :src="getImageUrl(task.publisherAvatar) || '/static/default-avatar.png'"
              mode="aspectFill"></image>
            <view class="user-detail">
              <text class="username">{{ task.publisherName }}</text>
              <text class="publish-info">{{ formatTime(task.createTime) }}</text>
            </view>
          </view>
          <view class="task-status" :class="getStatusClass(task.taskStatus)">
            {{ getStatusText(task.taskStatus) }}
          </view>
        </view>

        <view class="task-amount">¥{{ formatMoney(task.taskAmount) }}</view>
        <view class="task-title">{{ task.taskTitle }}</view>
        <view class="task-desc">{{ task.taskDesc }}</view>

        <view class="task-meta">
          <view class="meta-item">
            <u-icon name="map" size="20" color="#666"></u-icon>
            <text class="meta-text">{{ task.taskAddress }}</text>
          </view>
          <view class="meta-item" v-if="task.startTime">
            <u-icon name="clock" size="20" color="#666"></u-icon>
            <text class="meta-text">开始时间：{{ formatTime(task.startTime) }}</text>
          </view>
          <view class="meta-item" v-if="task.endTime">
            <u-icon name="clock" size="20" color="#666"></u-icon>
            <text class="meta-text">结束时间：{{ formatTime(task.endTime) }}</text>
          </view>
          <view class="meta-item">
            <u-icon name="eye" size="20" color="#666"></u-icon>
            <text class="meta-text">{{ task.viewCount || 0 }}次浏览</text>
          </view>
        </view>

        <!-- 任务类型信息 -->
        <view class="task-type-info">
          <view class="type-tags">
            <text class="type-tag primary" v-if="task.firstTypeName">{{ task.firstTypeName }}</text>
            <text class="type-tag secondary" v-if="task.secondTypeName">{{ task.secondTypeName }}</text>
            <text class="urgent-level-tag" :class="getUrgentLevelClass(task.urgentLevel)">
              🔥 {{ getUrgentLevelText(task.urgentLevel) }}
            </text>
          </view>
        </view>


      </view>

      <!-- 任务图片 -->
      <view class="task-images" v-if="task.taskImages && task.taskImages.length > 0">
        <view class="section-title">任务图片</view>
        <view class="image-grid">
          <image v-for="(image, index) in task.taskImages" :key="index" class="task-image"
            :src="getImageUrl(image.imageUrl)" mode="aspectFill" @click="previewImage(index)"></image>
        </view>
      </view>

      <!-- 接取者信息 -->
      <view class="receiver-info" v-if="task.receiverId && task.receiverName">
        <view class="section-title">接取者</view>
        <view class="receiver-detail">
          <text class="receiver-name">{{ task.receiverName }}</text>
          <text class="receive-time">接取时间：{{ formatTime(task.updateTime) }}</text>
        </view>
      </view>

      <!-- 联系方式 -->
      <view class="contact-info" v-if="showContact">
        <view class="section-title">联系方式</view>
        <view class="contact-item" @click="makeCall">
          <u-icon name="phone" size="32" color="#3cc51f"></u-icon>
          <text class="contact-text">拨打电话</text>
        </view>
      </view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="action-bar" v-if="showActionBar">
      <view class="action-buttons">
        <!-- 待接取状态 -->
        <template v-if="task.taskStatus === '0' && !isMyTask">
          <u-button type="primary" @click="acceptTask" :loading="actionLoading">
            接取任务
          </u-button>
        </template>

        <!-- 进行中状态 -->
        <template v-if="task.taskStatus === '1'">
          <u-button v-if="isReceiver" type="success" @click="completeTask" :loading="actionLoading">
            完成任务
          </u-button>
          <u-button v-if="isPublisher" type="warning" @click="cancelTask" :loading="actionLoading">
            取消任务
          </u-button>
        </template>

        <!-- 我发布的待接取任务 -->
        <template v-if="task.taskStatus === '0' && isMyTask">
          <u-button type="warning" @click="editTask">编辑任务</u-button>
          <u-button type="error" @click="cancelTask" :loading="actionLoading">取消任务</u-button>
        </template>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" v-else>
    <u-loading-icon size="60"></u-loading-icon>
    <text class="loading-text">加载中...</text>
  </view>
</template>

<script>
import { getTaskDetail, acceptTask, completeTask, cancelTask } from '@/api/task'
import { formatTime, formatMoney, checkLogin } from '@/utils/common'
import { getImageUrl } from '@/utils/request'

export default {
  data() {
    return {
      taskId: '',
      task: null,
      actionLoading: false,
      currentUserId: null,
      refreshing: false
    }
  },

  computed: {
    // 检查是否为任务发布者
    isPublisher() {
      return this.task && this.currentUserId &&
        String(this.task.publisherId) === String(this.currentUserId)
    },

    // 检查是否为我发布的任务（与isPublisher相同）
    isMyTask() {
      return this.isPublisher
    },

    // 检查是否为任务接取者
    isReceiver() {
      return this.task && this.currentUserId &&
        String(this.task.receiverId) === String(this.currentUserId)
    },

    // 是否显示联系方式（发布者或接取者可见）
    showContact() {
      return this.task && (this.isPublisher || this.isReceiver)
    },

    // 是否显示操作按钮（待接取或进行中状态）
    showActionBar() {
		console.log(this.currentUserId)
      return this.task && this.currentUserId &&
        (this.task.taskStatus === '0' || this.task.taskStatus === '1')
    }
  },

  onLoad(options) {
    this.taskId = options.id
    this.getCurrentUser()
    this.loadTaskDetail()
  },

  methods: {
    // 获取图片完整URL
    getImageUrl,

    getCurrentUser() {
      const userInfo = uni.getStorageSync('userInfo')
	  console.log(userInfo)
      if (userInfo) {
        this.currentUserId = userInfo.userId
      }
    },

    async loadTaskDetail() {
      try {
        const res = await getTaskDetail(this.taskId)
        if (res && res.data) {
          this.task = res.data
          console.log('任务详情数据:', this.task)
        } else {
          throw new Error('任务数据为空')
        }
      } catch (error) {
        console.error('加载任务详情失败:', error)
        uni.showToast({
          title: error.message || '加载失败',
          icon: 'none'
        })
        // 如果加载失败，可以选择返回上一页
        setTimeout(() => {
          uni.navigateBack()
        }, 2000)
      }
    },

    async onRefresh() {
      this.refreshing = true
      try {
        await this.loadTaskDetail()
      } finally {
        this.refreshing = false
      }
    },

    async acceptTask() {
      if (!checkLogin()) return

      uni.showModal({
        title: '确认接取',
        content: '确定要接取这个任务吗？',
        success: async (res) => {
          if (res.confirm) {
            this.actionLoading = true
            try {
              await acceptTask(this.taskId)
              uni.showToast({
                title: '接取成功',
                icon: 'success'
              })
              this.loadTaskDetail()
            } catch (error) {
              console.error('接取任务失败:', error)
              uni.showToast({
                title: error.message || '接取失败',
                icon: 'none'
              })
            } finally {
              this.actionLoading = false
            }
          }
        }
      })
    },

    async completeTask() {
      uni.showModal({
        title: '确认完成',
        content: '确定已完成这个任务吗？',
        success: async (res) => {
          if (res.confirm) {
            this.actionLoading = true
            try {
              await completeTask(this.taskId)
              uni.showToast({
                title: '任务已完成',
                icon: 'success'
              })
              this.loadTaskDetail()
            } catch (error) {
              console.error('完成任务失败:', error)
              uni.showToast({
                title: error.message || '操作失败',
                icon: 'none'
              })
            } finally {
              this.actionLoading = false
            }
          }
        }
      })
    },

    async cancelTask() {
      uni.showModal({
        title: '确认取消',
        content: '确定要取消这个任务吗？',
        success: async (res) => {
          if (res.confirm) {
            this.actionLoading = true
            try {
              await cancelTask(this.taskId)
              uni.showToast({
                title: '任务已取消',
                icon: 'success'
              })
              this.loadTaskDetail()
            } catch (error) {
              console.error('取消任务失败:', error)
              uni.showToast({
                title: error.message || '操作失败',
                icon: 'none'
              })
            } finally {
              this.actionLoading = false
            }
          }
        }
      })
    },

    editTask() {
      uni.navigateTo({
        url: `/pages/task/publish?id=${this.taskId}`
      })
    },

    makeCall() {
      // 这里应该从后端获取联系方式
      uni.showToast({
        title: '联系功能开发中',
        icon: 'none'
      })
    },

    previewImage(index) {
      if (!this.task.taskImages || this.task.taskImages.length === 0) return

      const urls = this.task.taskImages.map(image => this.getImageUrl(image.imageUrl))
      uni.previewImage({
        current: index,
        urls: urls
      })
    },

    getUrgentLevelText(level) {
      const levelMap = {
        '0': '普通',
        '1': '紧急',
        '2': '非常紧急'
      }
      return levelMap[level] || '普通'
    },

    getUrgentLevelClass(level) {
      const classMap = {
        '0': 'normal',
        '1': 'urgent',
        '2': 'very-urgent'
      }
      return classMap[level] || 'normal'
    },

    getStatusText(status) {
      const statusMap = {
        '0': '待接取',
        '1': '进行中',
        '2': '已完成',
        '3': '已取消'
      }
      return statusMap[status] || '未知'
    },

    getStatusClass(status) {
      const classMap = {
        '0': 'waiting',
        '1': 'processing',
        '2': 'completed',
        '3': 'cancelled'
      }
      return classMap[status] || ''
    },

    formatTime,
    formatMoney
  }
}
</script>

<style lang="scss" scoped>
.task-detail-container {
  height: 100vh;
  display: flex;
  background: #f8f8f8;
}

.detail-scroll {
  flex: 1;
  padding: 40rpx 40rpx 40rpx 40rpx;
}

.task-info {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;

    .user-info {
      display: flex;
      align-items: center;

      .avatar {
        width: 100rpx;
        height: 100rpx;
        border-radius: 50rpx;
        margin-right: 20rpx;
      }

      .user-detail {
        .username {
          display: block;
          font-size: 32rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 10rpx;
        }

        .publish-info {
          font-size: 26rpx;
          color: #999;
        }
      }
    }

    .task-status {
      padding: 15rpx 30rpx;
      border-radius: 30rpx;
      font-size: 26rpx;

      &.waiting {
        background: #e8f5e8;
        color: #3cc51f;
      }

      &.processing {
        background: #fff3e0;
        color: #ff9800;
      }

      &.completed {
        background: #e3f2fd;
        color: #2196f3;
      }

      &.cancelled {
        background: #ffebee;
        color: #f44336;
      }
    }
  }

  .task-amount {
    font-size: 48rpx;
    font-weight: bold;
    color: #ff6b35;
    margin-bottom: 20rpx;
  }

  .task-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
    line-height: 1.4;
  }

  .task-desc {
    font-size: 30rpx;
    color: #666;
    line-height: 1.6;
    margin-bottom: 30rpx;
  }

  .task-meta {
    .meta-item {
      display: flex;
      align-items: center;
      margin-bottom: 15rpx;

      .meta-text {
        margin-left: 15rpx;
        font-size: 28rpx;
        color: #666;
      }
    }
  }

  .task-type-info {
    margin-top: 30rpx;

    .section-title {
      font-size: 30rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 15rpx;
    }

    .type-tags {
      display: flex;
      gap: 15rpx;
      flex-wrap: wrap;

      .type-tag {
        padding: 10rpx 20rpx;
        border-radius: 20rpx;
        font-size: 24rpx;

        &.primary {
          background: #e3f2fd;
          color: #2196f3;
        }

        &.secondary {
          background: #f3e5f5;
          color: #9c27b0;
        }
      }
    }
  }

  .task-tags {
    display: flex;
    gap: 15rpx;
    margin-top: 20rpx;
    flex-wrap: wrap;
  }

  .urgent-tag {
    display: inline-block;
    background: linear-gradient(45deg, #ff6b35, #ff4757);
    color: #ffffff;
    padding: 15rpx 30rpx;
    border-radius: 30rpx;
    font-size: 26rpx;
  }

  .urgent-level-tag {
    padding: 10rpx 20rpx;
    border-radius: 20rpx;
    font-size: 24rpx;

    &.normal {
      background: #e8f5e8;
      color: #3cc51f;
    }

    &.urgent {
      background: #fff3e0;
      color: #ff9800;
    }

    &.very-urgent {
      background: #ffebee;
      color: #f44336;
    }
  }
}

.task-images {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }

  .image-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15rpx;

    .task-image {
      width: 100%;
      height: 200rpx;
      border-radius: 15rpx;
      background: #f5f5f5;
    }
  }
}

.receiver-info,
.contact-info {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }

  .receiver-detail {
    .receiver-name {
      display: block;
      font-size: 30rpx;
      color: #333;
      margin-bottom: 10rpx;
    }

    .receive-time {
      font-size: 26rpx;
      color: #999;
    }
  }

  .contact-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;

    .contact-text {
      margin-left: 15rpx;
      font-size: 30rpx;
      color: #3cc51f;
    }
  }
}

.action-bar {
  background: #ffffff;
  padding: 30rpx 40rpx;
  border-top: 1rpx solid #f0f0f0;

  .action-buttons {
    display: flex;
    gap: 20rpx;

    .u-button {
      flex: 1;
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;

  .loading-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #999;
  }
}
</style>
