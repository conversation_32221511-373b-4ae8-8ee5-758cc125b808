// 认证相关工具函数

const TOKEN_KEY = 'token'
const USER_INFO_KEY = 'userInfo'

// 获取token
export const getToken = () => {
  return uni.getStorageSync(TOKEN_KEY)
}

// 设置token
export const setToken = (token) => {
  return uni.setStorageSync(TOKEN_KEY, token)
}

// 移除token
export const removeToken = () => {
  return uni.removeStorageSync(TOKEN_KEY)
}

// 获取用户信息
export const getUserInfo = () => {
  const userInfo = uni.getStorageSync(USER_INFO_KEY)
  return userInfo ? JSON.parse(userInfo) : null
}

// 设置用户信息
export const setUserInfo = (userInfo) => {
  return uni.setStorageSync(USER_INFO_KEY, JSON.stringify(userInfo))
}

// 移除用户信息
export const removeUserInfo = () => {
  return uni.removeStorageSync(USER_INFO_KEY)
}

// 检查是否已登录
export const isLoggedIn = () => {
  return !!getToken()
}

// 清除所有认证信息
export const clearAuth = () => {
  removeToken()
  removeUserInfo()
}

// 登出
export const logout = () => {
  clearAuth()
  uni.reLaunch({
    url: '/pages/login/login'
  })
}
